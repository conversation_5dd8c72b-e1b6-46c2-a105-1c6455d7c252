# sql_evaluater/judge.py
from llm_factory import create_llm
from typing import Dict, Any
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import <PERSON>sonOutputParser
from pydantic import BaseModel, Field
import sys
from pathlib import Path
import pprint

# Add root directory to Python path for clean imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# 1. Define the desired structured output using Pydantic


class SQLEvaluation(BaseModel):
    """Structured format for the SQL query evaluation."""
    correctness_score: int = Field(
        description="Score from 1 (poor) to 5 (excellent) for logical correctness")
    correctness_reasoning: str = Field(
        description="Detailed reasoning for the correctness score")

    optimization_score: int = Field(
        description="Score from 1 (poor) to 5 (excellent) for performance and efficiency")
    optimization_reasoning: str = Field(
        description="Detailed reasoning for the optimization score")

    # readability_score: int = Field(
    #     description="Score from 1 (poor) to 5 (excellent) for performance and efficiency")
    # readability_reasoning: str = Field(
    #     description="Detailed reasoning for the optimization score")

    is_safe: bool = Field(
        description="True if the query is safe, False if it contains potential threats")
    security_reasoning: str = Field(
        description="Explanation of any potential security threats like DROP, DELETE without WHERE, etc.")

    final_verdict: str = Field(
        description="A final 'PASS' or 'FAIL' verdict based on the overall evaluation")


# 2. Implement the Judge class
class SQLJudge:
    def __init__(self, config: Dict[str, Any]):
        """
        Initializes the SQL Judge with flexible LLM support.

        Args:
            config: Dictionary containing model_name, temperature, and other LLM parameters
        """
        # Use the LLM factory to create the appropriate LLM instance
        self.llm = create_llm(config)
        self.model_name = config.get('model_name', 'unknown')
        # Create JSON parser for format instructions
        self.json_parser = JsonOutputParser(pydantic_object=SQLEvaluation)
        self.prompt_template = self._get_prompt_template()
        self.default_response = SQLEvaluation(
            correctness_score=3,
            correctness_reasoning="Could not evaluate due to parsing error",
            optimization_score=3,
            optimization_reasoning="Could not evaluate due to parsing error",
            is_safe=True,
            security_reasoning="Assumed safe due to parsing error",
            final_verdict="UNKNOWN"
        )

    def _get_prompt_template(self) -> ChatPromptTemplate:
        """Creates the detailed evaluation prompt (the rubric)."""
        # For Google Gemini models, we need to handle system messages differently
        # due to the deprecation of convert_system_message_to_human
        if 'gemini' in self.model_name.lower():
            # For Gemini, combine system and human messages into a single human message
            return ChatPromptTemplate.from_messages([
                ("human",
                 """You are an expert database administrator and senior data analyst. Your task is to meticulously evaluate a generated SQL query based on a user's request and a database schema.

                 You must evaluate across three criteria: Correctness, Optimization, and Security.

                 Evaluation Criteria Breakdown:
                 1.  **Correctness (Score 1-5):**
                     - Does the query correctly answer the user's question?
                     - Does it select the right columns and join the correct tables?
                     - Are the filtering conditions (`WHERE` clauses) accurate?
                 2.  **Optimization (Score 1-5):**
                     - Is the query efficient? Does it avoid unnecessary subqueries or complex operations where a simpler JOIN would suffice?
                     - Are indexes likely to be used effectively (hypothetically)?
                     - 5 is highly optimal, 1 is extremely inefficient.
                 3.  **Security (is_safe: true/false):**
                     - Does the query pose a threat? Look for `DROP`, `TRUNCATE`, `DELETE` without a `WHERE` clause, or other destructive operations.
                     - Read-only `SELECT` queries are generally safe.

                 Here is the information for your evaluation:

                 **Database Schema:**
                 ```sql
                 {schema}
                 ```

                 **User's Original Question:**
                 "{user_prompt}"

                 **Generated SQL Query to Evaluate:**
                 ```sql
                 {generated_sql}
                 ```

                 {format_instructions}
                 """)
            ])
        else:
            # For other LLMs (OpenAI, Anthropic, etc.), use separate system and human messages
            return ChatPromptTemplate.from_messages([
                ("system",
                 """You are an expert database administrator and senior data analyst. Your task is to meticulously evaluate a generated SQL query based on a user's request and a database schema.

                 You must evaluate across three criteria: Correctness, Optimization, and Security.

                 Evaluation Criteria Breakdown:
                 1.  **Correctness (Score 1-5):**
                     - Does the query correctly answer the user's question?
                     - Does it select the right columns and join the correct tables?
                     - Are the filtering conditions (`WHERE` clauses) accurate?
                 2.  **Optimization (Score 1-5):**
                     - Is the query efficient? Does it avoid unnecessary subqueries or complex operations where a simpler JOIN would suffice?
                     - Are indexes likely to be used effectively (hypothetically)?
                     - 5 is highly optimal, 1 is extremely inefficient.
                 3.  **Security (is_safe: true/false):**
                     - Does the query pose a threat? Look for `DROP`, `TRUNCATE`, `DELETE` without a `WHERE` clause, or other destructive operations.
                     - Read-only `SELECT` queries are generally safe.
                 """),
                ("human",
                 """
                 Here is the information for your evaluation:

                 **Database Schema:**
                 ```sql
                 {schema}
                 ```

                 **User's Original Question:**
                 "{user_prompt}"

                 **Generated SQL Query to Evaluate:**
                 ```sql
                 {generated_sql}
                 ```

                 {format_instructions}
                 """)
            ])

    def evaluate(self, schema: str, user_prompt: str, generated_sql: str) -> SQLEvaluation:
        """Runs the evaluation chain with JSON parsing."""
        # Create the chain with JSON output parser
        chain = self.prompt_template | self.llm | self.json_parser

        try:
            # Get the response - JsonOutputParser will handle JSON parsing automatically
            response = chain.invoke({
                "schema": schema,
                "user_prompt": user_prompt,
                "generated_sql": generated_sql,
                "format_instructions": self.json_parser.get_format_instructions()
            })

            # JsonOutputParser returns a dictionary, so we create the Pydantic model from it
            return SQLEvaluation(**response)

        except Exception as e:
            print(f"⚠️  Warning: Could not parse JSON response: {e}")
            print(f"📝 Error details: {str(e)}")
            print(
                f"Returning default response: {pprint.pprint(self.default_response.model_dump())}")

        #     # Fallback: try to get raw response for debugging
        #     try:
        #         from langchain_core.output_parsers import StrOutputParser
        #         fallback_chain = self.prompt_template | self.llm | StrOutputParser()
        #         raw_response = fallback_chain.invoke({
        #             "schema": schema,
        #             "user_prompt": user_prompt,
        #             "generated_sql": generated_sql,
        #             "format_instructions": self.json_parser.get_format_instructions()
        #         })
        #         print(f"📝 Raw response: {raw_response[:500]}...")
        #     except Exception as fallback_error:
        #         print(f"⚠️  Could not get raw response: {fallback_error}")

            # Return a default evaluation
            return SQLEvaluation(
                correctness_score=3,
                correctness_reasoning="Could not evaluate due to parsing error",
                optimization_score=3,
                optimization_reasoning="Could not evaluate due to parsing error",
                is_safe=True,
                security_reasoning="Assumed safe due to parsing error",
                final_verdict="UNKNOWN"
            )
